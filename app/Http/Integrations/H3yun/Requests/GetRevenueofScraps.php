<?php

namespace App\Http\Integrations\H3yun\Requests;

use Saloon\Enums\Method;
use Saloon\Http\Request;

class GetRevenueofScraps extends LoadBizObjects
{
    public function __construct(
        protected int $from = 1,
        protected int $to = 100,
        protected ?array $filter = null,
    ) {
        parent::__construct('D000867f3d89c4dc2f843b6817a1f3d9c521361', $from, $to, $filter);
    }
}
